# Complete Skill-Sage Testing Guide

## 🎯 What's Now Working

### ✅ **Dashboard (React)**
- **External Jobs Navigation**: Added to sidebar menu for EMPLOYER and ADMIN roles
- **Job Scraping**: Click "Scrape New Jobs" button to fetch external jobs
- **Job Management**: View, filter, and manage both internal and external jobs
- **Source Filtering**: Filter jobs by source (StackOverflow, We Work Remotely, etc.)
- **Statistics**: View job counts and analytics

### ✅ **Flutter App**
- **Enhanced Home Screen**: Shows both skill and job recommendations
- **External Job Indicators**: Blue badges for external jobs, green for internal
- **Match Scores**: Displays compatibility percentage for each job
- **Functional Search**: Search works in both home and job screens
- **External Jobs Tab**: New bottom navigation tab for external jobs
- **Apply Links**: Clicking external jobs shows apply URL dialog

### ✅ **Backend (FastAPI)**
- **Job Scraping Service**: Scrapes from multiple sources
- **External Job Endpoints**: Complete API for external job management
- **Combined Recommendations**: Merges internal and external jobs
- **Match Scoring**: Intelligent job-user compatibility scoring

## 🧪 Complete Testing Flow

### **Step 1: Setup and Data Preparation**

#### 1.1 Start Backend
```bash
cd skill_sage
uvicorn main:app --host 0.0.0.0 --port 8004 --reload
```

#### 1.2 Scrape External Jobs
```bash
# Via API
curl -X POST http://localhost:8004/user/scrape_jobs \
  -H "Authorization: Bearer YOUR_TOKEN"

# Expected response:
{
  "success": true,
  "result": "Scraped and saved 15 new jobs from 25 total"
}
```

#### 1.3 Verify Scraped Data
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/external_jobs?limit=5
```

### **Step 2: Test Dashboard Features**

#### 2.1 Access Dashboard
1. Open `http://localhost:3000`
2. Login with EMPLOYER or ADMIN account
3. Navigate to **"External Jobs"** in sidebar

#### 2.2 Test Job Scraping
1. Click **"Scrape New Jobs"** button
2. Verify success notification appears
3. Check that new jobs are displayed in table

#### 2.3 Test Job Management
1. **View Jobs**: See all external jobs with apply links
2. **Filter by Source**: Use dropdown to filter by job board
3. **Search Jobs**: Use search bar to find specific jobs
4. **View Statistics**: Check job count cards at top

#### 2.4 Test Recommendations
1. Switch to **"Recommended Jobs"** tab
2. Verify match scores are displayed
3. Check that jobs are sorted by match score

### **Step 3: Test Flutter App Features**

#### 3.1 Setup Flutter App
```bash
cd Skill-Sage
flutter run
```

#### 3.2 Test Enhanced Home Screen
1. **Login** with job seeker account
2. **Add Skills** to your profile (Python, JavaScript, React, etc.)
3. **Home Screen**: Verify you see:
   - Horizontal skill recommendations at top
   - Job recommendations below with match scores
   - External/Internal indicators (blue/green badges)

#### 3.3 Test Search Functionality
1. **Search Bar**: Type in search bar at top of home screen
2. **Real-time Filtering**: Jobs should filter as you type
3. **Search Terms**: Try searching for:
   - Job titles: "Developer", "Engineer"
   - Companies: "TechCorp", "Speechify"
   - Skills: "Python", "React"
   - Locations: "Remote", "San Francisco"

#### 3.4 Test Job Interactions
1. **External Jobs**: Tap on jobs with blue "External" badge
2. **Apply Dialog**: Should show URL dialog with apply link
3. **Internal Jobs**: Tap on jobs with green "Internal" badge
4. **Job Details**: Should show job details modal

#### 3.5 Test External Jobs Tab
1. **Navigation**: Tap globe icon in bottom navigation
2. **Two Tabs**: 
   - "All Jobs": Shows all external jobs
   - "Recommended": Shows jobs with match scores
3. **Refresh**: Pull down to refresh job list
4. **Filter Menu**: Tap filter icon to filter by source

#### 3.6 Test Job Posts Screen
1. **Navigation**: Tap briefcase icon in bottom navigation
2. **Search**: Use search bar to filter internal jobs
3. **Filter Button**: Tap filter icon to open filter screen
4. **Apply Filter**: Tap "Apply Filter" button (shows coming soon message)

### **Step 4: Test API Endpoints**

#### 4.1 Job Recommendations
```bash
# Get combined recommendations (internal + external)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/recommend_all_jobs?limit=10

# Expected response includes both internal and external jobs with match scores
```

#### 4.2 External Jobs Only
```bash
# Get external jobs
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/external_jobs?limit=10

# Filter by source
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8004/user/external_jobs?source=Remote OK"
```

#### 4.3 Job Preferences
```bash
# Set job preferences
curl -X POST http://localhost:8004/user/job_preferences \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "preferred_locations": ["Remote", "New York"],
    "preferred_job_types": ["FULL_TIME"],
    "min_salary": 70000,
    "max_salary": 120000,
    "remote_ok": true
  }'
```

### **Step 5: Verify Data Flow**

#### 5.1 Check Database
```sql
-- Connect to PostgreSQL
psql -U skill_sage_user -d skill_sage

-- Check external jobs
SELECT id, title, company, source, apply_url FROM external_jobs LIMIT 5;

-- Check job matches
SELECT user_id, external_job_id, match_score FROM external_job_matches LIMIT 5;

-- Check user preferences
SELECT user_id, preferred_locations, remote_ok FROM user_job_preferences;
```

#### 5.2 Verify Job Sources
Expected sources in database:
- ✅ **StackOverflow**: Mock data with skills
- ✅ **Remote OK**: Live scraped data
- ✅ **We Work Remotely**: Live scraped data  
- ✅ **Greenhouse**: Company-specific data

## 🎯 Expected Results

### **Dashboard Should Show:**
- External Jobs navigation item
- Scrape button working
- Job table with apply URLs
- Filter and search functionality
- Job statistics and analytics

### **Flutter App Should Show:**
- Enhanced home with job recommendations
- External/Internal job indicators
- Functional search across all screens
- External jobs tab with two sub-tabs
- Match scores and apply dialogs

### **API Should Return:**
- Combined job recommendations
- External jobs with apply URLs
- Match scores between 0-100%
- Proper filtering by source

## 🚨 Troubleshooting

### **No External Jobs Showing:**
1. Check if scraping completed successfully
2. Verify external_jobs table has data
3. Check user has skills in profile for recommendations

### **Search Not Working:**
1. Verify search callback is passed to CustomAppHeader
2. Check filteredJobs state is updating
3. Ensure search query is being processed

### **Dashboard Navigation Missing:**
1. Check user role (EMPLOYER or ADMIN required)
2. Verify ExternalJobs component is imported
3. Check menu item key is correct

### **Flutter External Jobs Tab Not Working:**
1. Verify ExternalJobsScreen is added to _index.dart
2. Check provider references are correct
3. Ensure external jobs data is loaded

## ✅ Success Criteria

The system is working correctly when:
- ✅ Dashboard shows external jobs with scraping functionality
- ✅ Flutter app displays job recommendations with indicators
- ✅ Search works across all screens
- ✅ External job apply links are functional
- ✅ Match scores are calculated and displayed
- ✅ API endpoints return proper data structure

**The enhanced Skill-Sage system now provides a complete job ecosystem with intelligent recommendations and external job integration!** 🎉
