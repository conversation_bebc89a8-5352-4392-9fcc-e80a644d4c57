# Enhanced Skill-Sage with Job Scraping - Complete Setup Guide

## 🚀 New Features Added

### ✅ Job Scraping System
- **Sources**: StackOverflow, We Work Remotely, Remote OK, Greenhouse
- **Auto-scraping**: Fetches jobs with skills, salary, location, and apply URLs
- **Smart deduplication**: Prevents duplicate job entries

### ✅ Enhanced Recommendations
- **Combined recommendations**: Internal + External jobs
- **Match scoring**: 0-100% compatibility based on user skills
- **External application**: Direct links to original job postings

### ✅ Multi-Platform Support
- **Backend**: New scraping service and external job endpoints
- **Dashboard**: External job management and analytics
- **Flutter**: External job browsing with apply links

## 📋 Prerequisites

### Required Dependencies
```bash
# Backend (skill_sage)
pip install requests beautifulsoup4 lxml

# Flutter (Skill-Sage) 
flutter pub add url_launcher

# Dashboard (React) - already included
```

## 🛠️ Setup Instructions

### 1. Database Setup
```bash
# The new tables will be auto-created by SQLAlchemy:
# - external_jobs
# - external_job_matches
# - job_matches (already added)
# - user_job_preferences (already added)

# Start PostgreSQL and create database as before
createdb skill_sage
```

### 2. Backend Setup (skill_sage)
```bash
cd skill_sage

# Install new dependencies
pip install requests beautifulsoup4 lxml

# Start server
uvicorn main:app --host 0.0.0.0 --port 8004 --reload
```

### 3. Dashboard Setup
```bash
cd dashboard
npm install  # or yarn install
npm start    # or yarn start
```

### 4. Flutter Setup
```bash
cd Skill-Sage

# Add URL launcher for external job links
flutter pub add url_launcher

# For iOS, add to ios/Runner/Info.plist:
<key>LSApplicationQueriesSchemes</key>
<array>
    <string>https</string>
    <string>http</string>
</array>

# Run app
flutter run
```

## 🧪 Testing the Enhanced System

### 1. Test Job Scraping (Backend)

#### Manual Scraping Test:
```bash
cd skill_sage
python services/job_scraper.py
```

#### API Scraping Test:
```bash
# Scrape jobs via API
curl -X POST http://localhost:8004/user/scrape_jobs \
  -H "Authorization: Bearer YOUR_TOKEN"

# Expected response:
{
  "success": true,
  "result": "Scraped and saved 15 new jobs from 25 total"
}
```

### 2. Test External Job Endpoints

#### Get External Jobs:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/external_jobs?limit=10

# Filter by source:
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:8004/user/external_jobs?source=We Work Remotely"
```

#### Get External Job Recommendations:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/recommend_external_jobs?limit=10
```

#### Get Combined Recommendations:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/recommend_all_jobs?limit=20
```

### 3. Test Dashboard Features

#### Access External Jobs Page:
1. Login to dashboard at `http://localhost:3000`
2. Navigate to External Jobs section
3. Click "Scrape New Jobs" button
4. View scraped jobs with apply links
5. Filter by job source
6. View recommended jobs with match scores

### 4. Test Flutter App Features

#### Test External Jobs Screen:
1. Open Flutter app
2. Navigate to External Jobs (add to navigation)
3. View "All Jobs" tab with external jobs
4. View "Recommended" tab with match scores
5. Tap "Apply" button to open external job links
6. Use refresh to reload jobs
7. Filter by job source using menu

### 5. Test Complete Job Recommendation Flow

#### End-to-End Test:
1. **Setup User Profile**:
   - Register new user
   - Add skills: ["Python", "Django", "React", "JavaScript"]
   - Upload CV

2. **Scrape Jobs**:
   ```bash
   curl -X POST http://localhost:8004/user/scrape_jobs \
     -H "Authorization: Bearer YOUR_TOKEN"
   ```

3. **Get Recommendations**:
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        http://localhost:8004/user/recommend_all_jobs?limit=10
   ```

4. **Verify Results**:
   - Jobs should have match scores
   - External jobs should have `apply_url`
   - Internal jobs should have `is_external: false`

## 📱 Flutter Navigation Update

Add external jobs to your Flutter navigation:

```dart
// In your main navigation or drawer
ListTile(
  leading: Icon(Icons.work_outline),
  title: Text('External Jobs'),
  onTap: () {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExternalJobsScreen(),
      ),
    );
  },
),
```

## 🔧 Configuration Options

### Job Scraper Configuration
```python
# In skill_sage/services/job_scraper.py
# Modify these settings:

# Number of jobs per source
limit_per_source = 10

# Scraping frequency (implement with cron job)
# 0 */6 * * * python skill_sage/services/job_scraper.py

# Add new job sources by extending the scraper class
```

### Rate Limiting (Recommended)
```python
# Add to job scraper for production:
import time
time.sleep(2)  # 2 second delay between requests
```

## 🚨 Important Notes

### 1. Respectful Scraping
- **Rate limiting**: Built-in delays between requests
- **User-Agent**: Proper browser headers
- **Terms of Service**: Ensure compliance with each site's ToS

### 2. Data Accuracy
- **Job freshness**: Scraped jobs may become outdated
- **Apply URLs**: Always verify links work before user interaction
- **Skill extraction**: Simple keyword matching (can be improved)

### 3. Production Considerations
- **Caching**: Cache external jobs for 1-6 hours
- **Background jobs**: Use Celery for scheduled scraping
- **Error handling**: Graceful failures when sites are down

## 🔄 Automated Job Scraping (Optional)

### Setup Cron Job:
```bash
# Add to crontab (crontab -e)
# Scrape jobs every 6 hours
0 */6 * * * cd /path/to/skill_sage && python services/job_scraper.py

# Or use the API endpoint
0 */6 * * * curl -X POST http://localhost:8004/user/scrape_jobs -H "Authorization: Bearer ADMIN_TOKEN"
```

## 📊 Analytics and Monitoring

### Track Job Scraping Success:
```sql
-- Check scraped jobs
SELECT source, COUNT(*) as job_count, MAX(scraped_date) as last_scraped
FROM external_jobs 
WHERE is_active = true
GROUP BY source;

-- Check recommendation performance
SELECT 
  AVG(match_score) as avg_match_score,
  COUNT(*) as total_matches
FROM external_job_matches;
```

## 🎯 Testing Checklist

- [ ] Backend scraper runs without errors
- [ ] External jobs stored in database
- [ ] API endpoints return correct data
- [ ] Dashboard displays external jobs
- [ ] Flutter app shows external jobs
- [ ] Apply links open correctly
- [ ] Match scores calculated properly
- [ ] Job recommendations work
- [ ] Filtering by source works
- [ ] Refresh functionality works

## 🚀 Next Steps

1. **Test the complete flow** using this guide
2. **Add navigation** to external jobs in Flutter
3. **Setup automated scraping** with cron jobs
4. **Monitor job quality** and adjust scraping logic
5. **Add more job sources** as needed

The enhanced Skill-Sage system now provides comprehensive job matching with external opportunities, making it a complete career platform! 🎉
