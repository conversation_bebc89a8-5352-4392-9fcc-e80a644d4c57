# External Jobs Troubleshooting Guide

## 🔍 Issue: External Jobs Not Showing in Flutter App

The external jobs are working in the React dashboard but not appearing in the Flutter app. Here's what we've implemented and how to debug:

## ✅ **What Was Fixed**

### 1. **Backend Changes**
- **Removed Match Score Filter**: External jobs now appear even with 0% match score
- **Added Job Status Fields**: `is_enabled` and `is_active` fields in API response
- **Fixed Filtering Logic**: All enabled external jobs are now included in recommendations

### 2. **Flutter App Enhancements**
- **Enhanced Job Cards**: Better external/internal indicators with icons
- **Improved Match Score Display**: Shows "New Job" for external jobs with 0% match
- **Debug Logging**: Added extensive logging to track job loading
- **Simplified Filtering**: Removed complex filters, now shows all jobs

### 3. **Visual Improvements**
- **Status Badges**: Blue badges with globe icon for external jobs
- **Green Badges**: Business icon for internal jobs
- **Match Score Labels**: "New Job" for external jobs without skills

## 🧪 **Testing Steps**

### **Step 1: Verify Backend is Working**
```bash
# Test external jobs endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/external_jobs?limit=10

# Test combined recommendations
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/recommend_all_jobs?limit=20
```

### **Step 2: Check Job Status in Dashboard**
1. Open React dashboard at `http://localhost:3000`
2. Navigate to "External Jobs"
3. Verify jobs are **enabled** (green status)
4. If disabled, select jobs and click "Enable Selected"

### **Step 3: Debug Flutter App**
1. Run Flutter app with debug console open
2. Look for these debug messages:
   ```
   Loading all recommended jobs with limit: 20
   API response success: true
   API response result length: X
   External jobs in response: Y
   Internal jobs in response: Z
   ```

### **Step 4: Check Flutter App Display**
1. Open home screen in Flutter app
2. Look for job cards with:
   - Blue badges with globe icon (External)
   - Green badges with business icon (Internal)
   - "New Job" labels for external jobs

## 🔧 **Common Issues & Solutions**

### **Issue 1: No External Jobs in API Response**
**Symptoms**: API returns 0 external jobs
**Solutions**:
1. Check if jobs are enabled in dashboard
2. Verify jobs exist: `SELECT * FROM external_jobs WHERE is_enabled = true;`
3. Run job scraper: `curl -X POST http://localhost:8004/user/scrape_jobs`

### **Issue 2: Authentication Problems**
**Symptoms**: API returns 401 or empty results
**Solutions**:
1. Check Flutter app token: Look for "token == " in debug console
2. Verify base URL in `.env` file: `BASE_URL=http://************:8004`
3. Test API manually with valid token

### **Issue 3: Jobs Filtered Out**
**Symptoms**: API returns jobs but Flutter shows none
**Solutions**:
1. Check search query is empty
2. Verify `filteredJobs` contains external jobs
3. Look for filtering logic issues

### **Issue 4: UI Not Updating**
**Symptoms**: Jobs load but UI doesn't show them
**Solutions**:
1. Check `setState()` is called after loading
2. Verify `allJobs` and `filteredJobs` are populated
3. Check for widget rebuild issues

## 📱 **Expected Flutter App Behavior**

### **Home Screen Should Show**:
- Mixed list of internal and external jobs
- Blue badges with globe icon for external jobs
- Green badges with business icon for internal jobs
- Match scores or "New Job" labels
- WebView opens when clicking external job apply buttons

### **Job Cards Should Display**:
```
[Job Title]
[Company Name]
📍 [Location]

[Skills chips...]

💰 [Salary] | 🎯 [Match%/New Job] | 🌐 External
                                    Apply Button
```

## 🔍 **Debug Commands**

### **Check Database**
```sql
-- Check external jobs
SELECT id, title, company, is_enabled, is_active FROM external_jobs LIMIT 5;

-- Check enabled jobs count
SELECT COUNT(*) FROM external_jobs WHERE is_enabled = true AND is_active = true;
```

### **Test API Endpoints**
```bash
# Get external jobs
curl -H "Authorization: Bearer TOKEN" http://localhost:8004/user/external_jobs

# Get combined recommendations  
curl -H "Authorization: Bearer TOKEN" http://localhost:8004/user/recommend_all_jobs

# Scrape new jobs
curl -X POST -H "Authorization: Bearer TOKEN" http://localhost:8004/user/scrape_jobs
```

### **Flutter Debug Console**
Look for these messages:
- `Loading all recommended jobs with limit: 20`
- `External jobs in response: X`
- `Loaded X jobs`
- `External jobs: Y`

## 🚀 **Quick Fix Checklist**

1. ✅ **Backend**: External jobs included without match score filter
2. ✅ **Database**: Jobs are enabled (`is_enabled = true`)
3. ✅ **API**: Returns external jobs in response
4. ✅ **Flutter**: Debug logging shows jobs loading
5. ✅ **UI**: Job cards display with proper badges
6. ✅ **WebView**: External jobs open in-app browser

## 📞 **If Still Not Working**

1. **Check the debug console** for specific error messages
2. **Verify API responses** match expected format
3. **Test with fresh job scraping** to ensure data exists
4. **Check user authentication** and permissions
5. **Verify network connectivity** between Flutter and backend

The external jobs should now appear in the Flutter app with clear visual indicators and proper functionality! 🎯
