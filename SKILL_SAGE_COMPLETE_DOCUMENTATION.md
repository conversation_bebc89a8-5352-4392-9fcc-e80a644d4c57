# Skill-Sage Complete System Documentation

## Overview

<!-- skill_sage,skills_recommender,Skill-Sage and dashboard -->

Skill-Sage is a comprehensive job matching and skill development platform consisting of three main components:

1. **skill_sage** - FastAPI backend server
2. **skills_recommender** - Python-based skill recommendation engine
3. **Skill-Sage** - Flutter mobile application

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │◄──►│  FastAPI Backend │◄──►│ PostgreSQL DB   │
│   (Skill-Sage)  │    │   (skill_sage)   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │skills_recommender│
                       │   (Python)       │
                       └─────────────────┘
```

## Component Details

### 1. FastAPI Backend (skill_sage)

**Location**: `skill_sage/`

**Key Features**:

- User authentication and authorization
- Job posting and management
- Skill management and tracking
- Resume/CV upload and storage
- Skill recommendations (currently implemented)
- Course management

**Main Files**:

- `main.py` - Application entry point
- `db/connection.py` - Database configuration and recommendation logic
- `models/user.py` - User, JobSeeker, Skills, and related models
- `models/job.py` - Job, JobApplication, Bookmark models
- `routes/user_routes.py` - User-related endpoints
- `routes/job.py` - Job-related endpoints
- `routes/auth.py` - Authentication endpoints

**Database Models**:

- `users` - User accounts with roles (JOB_SEEKER, EMPLOYER, ADMIN, etc.)
- `job_seekers` - Extended profile information for job seekers
- `skills` - Master list of skills
- `job_seeker_skills` - User-skill relationships
- `jobs` - Job postings with required skills
- `job_applications` - Job application tracking
- `skill_factors` - Skill correlation data for recommendations
- `files` - File storage for resumes and images

### 2. Skills Recommender (skills_recommender)

**Location**: `skills_recommender/`

**Purpose**: Generates skill recommendations based on user's current skills using correlation analysis.

**Key Files**:

- `src/recommender.py` - Main recommendation algorithm
- `src/database.py` - Database setup for skill factors
- `src/main.py` - Data processing pipeline
- `new_factors.json` - Skill correlation factors
- `case_pair.json` - Skill name mappings

**Algorithm**:

1. Takes user's current skills as input
2. Looks up correlation factors from `skill_factors` table
3. Calculates weighted averages for related skills
4. Returns top N recommended skills

### 3. Flutter App (Skill-Sage)

**Location**: `Skill-Sage/`

**Key Features**:

- User registration and login
- Profile management with skill selection
- Resume/CV upload
- Job browsing and application
- Skill recommendations display
- Course browsing

**Main Directories**:

- `lib/providers/` - State management (Riverpod)
- `lib/screens/` - UI screens
- `lib/models/` - Data models
- `lib/utils/` - Utilities and themes

**Key Providers**:

- `HttpProvider` - API communication
- `AuthProvider` - Authentication state
- `JobProvider` - Job-related operations
- `RecommenderProvider` - Skill recommendations

## Current System Flow

### User Registration & Profile Setup

1. User registers through Flutter app
2. User selects skills and uploads CV
3. Data stored in PostgreSQL database
4. User profile created with skill associations

### Skill Recommendations

1. User requests skill recommendations
2. Flutter app calls `/user/recommend_skills` endpoint
3. Backend retrieves user's current skills
4. Recommendation algorithm calculates related skills
5. Top recommendations returned to user

### Job Management

1. Employers post jobs with required skills
2. Job seekers browse available jobs
3. Users can bookmark and apply for jobs
4. Applications tracked in database

## Current Limitations

### ❌ Missing Job Recommendations

- **Problem**: System only recommends skills, not jobs
- **Impact**: Users must manually browse jobs
- **Gap**: No intelligent job matching based on user skills and CV

### ❌ Limited CV Processing

- **Problem**: CVs are stored but not analyzed
- **Impact**: Rich CV data not utilized for matching
- **Gap**: No skill extraction from uploaded resumes

### ❌ Basic Matching Algorithm

- **Problem**: No sophisticated job-user matching
- **Impact**: Poor job discovery experience
- **Gap**: No scoring or ranking of job matches

## Database Setup Requirements

### PostgreSQL Configuration

```sql
-- Required tables (auto-created by SQLAlchemy)
- users
- job_seekers
- skills
- job_seeker_skills
- jobs
- job_applications
- skill_factors (populated by skills_recommender)
- files
```

### Environment Variables

```bash
# skill_sage/.env
POSTGRES_URL=postgresql://username:password@localhost:5432/skill_sage

# Skill-Sage/.env
BASE_URL=http://localhost:8004
```

## Setup Instructions

### 1. Database Setup

```bash
# Install PostgreSQL
# Create database
createdb skill_sage

# Run skills_recommender to populate skill data
cd skills_recommender/src
python database.py
```

### 2. Backend Setup

```bash
cd skill_sage
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8004
```

### 3. Flutter App Setup

```bash
cd Skill-Sage
flutter pub get
flutter run
```

## API Endpoints

### Authentication

- `POST /auth/register` - User registration
- `POST /auth/login` - User login

### User Management

- `GET /user/profile` - Get user profile
- `POST /user/skills` - Add user skills
- `POST /user/upload_resume` - Upload CV
- `GET /user/recommend_skills` - Get skill recommendations

### Job Management

- `GET /job/` - List all jobs
- `POST /job/` - Create job (employers only)
- `POST /job/application/{job_id}` - Apply for job
- `GET /job/applications` - Get user's applications

## Testing the System

### 1. Test User Registration

```bash
# Use Flutter app or API directly
curl -X POST http://localhost:8004/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123"}'
```

### 2. Test Skill Recommendations

1. Login to Flutter app
2. Add some skills to your profile
3. Navigate to home screen
4. View recommended skills section

### 3. Test Job Application Flow

1. Browse jobs in Flutter app
2. Apply for a job
3. Check applications in profile

## Current System Analysis

### ✅ What Works Well

1. **Skill Recommendation Engine**: Sophisticated algorithm using correlation factors
2. **User Management**: Complete authentication and profile system
3. **Job Posting**: Employers can post jobs with skill requirements
4. **CV Upload**: Users can upload resumes (stored as files)
5. **Job Applications**: Basic application tracking system

### ❌ Critical Missing Features

#### 1. Job Recommendation System

**Current State**: Users must manually browse all jobs
**Problem**: No intelligent matching between user skills and job requirements
**Impact**: Poor user experience, missed opportunities

#### 2. CV Analysis and Skill Extraction

**Current State**: CVs stored as binary files, not processed
**Problem**: Rich skill data in CVs not utilized
**Impact**: Incomplete user skill profiles

#### 3. Job Matching Algorithm

**Current State**: No scoring or ranking of job relevance
**Problem**: All jobs shown equally regardless of user fit
**Impact**: Users overwhelmed with irrelevant jobs

#### 4. Skill Gap Analysis

**Current State**: No comparison between user skills and job requirements
**Problem**: Users don't know what skills they need for desired jobs
**Impact**: No guidance for skill development

## Proposed Job Recommendation System

### Architecture Enhancement

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │◄──►│  FastAPI Backend │◄──►│ PostgreSQL DB   │
│                 │    │                 │    │                 │
│ + Job Recs      │    │ + Job Matching  │    │ + Match Scores  │
│ + Match Scores  │    │ + CV Analysis   │    │ + User Prefs    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │Enhanced Recommender│
                       │ + Job Matching   │
                       │ + CV Processing  │
                       └─────────────────┘
```

### New Database Tables Needed

```sql
-- Job match scores for users
CREATE TABLE job_matches (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    job_id INTEGER REFERENCES jobs(id),
    match_score FLOAT,
    skill_match_count INTEGER,
    missing_skills TEXT[],
    created TIMESTAMP DEFAULT NOW()
);

-- User job preferences
CREATE TABLE user_job_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    preferred_locations TEXT[],
    preferred_job_types TEXT[],
    min_salary FLOAT,
    max_salary FLOAT,
    remote_ok BOOLEAN DEFAULT FALSE
);
```

### New API Endpoints Needed

```python
# Get personalized job recommendations
GET /user/recommend_jobs?limit=20

# Get job match details
GET /job/{job_id}/match_score

# Update job preferences
POST /user/job_preferences

# Get skill gap analysis for a job
GET /job/{job_id}/skill_gap
```

### Job Matching Algorithm

1. **Skill Overlap Calculation**

   - Compare user skills with job required skills
   - Weight by skill importance/frequency
   - Calculate percentage match

2. **Location & Preference Filtering**

   - Filter by user location preferences
   - Apply salary range filters
   - Consider remote work preferences

3. **Experience Level Matching**

   - Match user experience with job requirements
   - Consider education background

4. **Scoring Formula**
   ```python
   match_score = (
       skill_match_weight * skill_overlap_percentage +
       location_weight * location_match +
       salary_weight * salary_fit +
       experience_weight * experience_match
   ) * 100
   ```

## Implementation Plan

### Phase 1: Basic Job Recommendations

1. Create job matching algorithm
2. Add `/user/recommend_jobs` endpoint
3. Update Flutter app to show recommended jobs
4. Add match scores to job listings

### Phase 2: Enhanced Matching

1. Add user preference management
2. Implement skill gap analysis
3. Add CV text extraction and skill parsing
4. Create job match explanations

### Phase 3: Advanced Features

1. Machine learning-based matching
2. Job alert notifications
3. Skill development recommendations
4. Employer-side candidate recommendations

## Testing Strategy

### 1. Create Test Data

```sql
-- Insert test jobs with various skill requirements
-- Create test users with different skill sets
-- Verify matching algorithm accuracy
```

### 2. API Testing

```bash
# Test job recommendations
curl -H "Authorization: Bearer <token>" \
     http://localhost:8004/user/recommend_jobs

# Test match scoring
curl -H "Authorization: Bearer <token>" \
     http://localhost:8004/job/1/match_score
```

### 3. Flutter App Testing

1. Login with test user
2. Navigate to recommended jobs section
3. Verify match scores display correctly
4. Test job application flow

This enhanced system would transform Skill-Sage from a basic job board into an intelligent career platform that actively helps users find relevant opportunities and develop their skills.
