# Skill-Sage Complete Setup and Testing Guide

## Prerequisites

### System Requirements
- **Python 3.8+** (for FastAPI backend)
- **PostgreSQL 12+** (database)
- **Flutter 3.0+** (mobile app)
- **Node.js 16+** (optional, for web dashboard)

### Development Tools
- **VS Code** or **PyCharm** (Python development)
- **Android Studio** or **VS Code** (Flutter development)
- **pgAdmin** or **DBeaver** (database management)

## Step 1: Database Setup

### 1.1 Install PostgreSQL
```bash
# macOS
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql

# Windows
# Download from https://www.postgresql.org/download/windows/
```

### 1.2 Create Database
```bash
# Connect to PostgreSQL
psql -U postgres

# Create database and user
CREATE DATABASE skill_sage;
CREATE USER skill_sage_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE skill_sage TO skill_sage_user;
\q
```

### 1.3 Configure Environment Variables
Create `.env` file in `skill_sage/` directory:
```bash
# skill_sage/.env
POSTGRES_URL=postgresql://skill_sage_user:your_password@localhost:5432/skill_sage
JWT_SECRET=your_jwt_secret_key_here
```

## Step 2: Skills Recommender Setup

### 2.1 Setup Python Environment
```bash
cd skills_recommender
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install psycopg2-binary pandas numpy
```

### 2.2 Initialize Skill Data
```bash
cd skills_recommender/src
python database.py
```

This will create and populate:
- `skills` table with skill names
- `skill_factors` table with correlation data

## Step 3: FastAPI Backend Setup

### 3.1 Install Dependencies
```bash
cd skill_sage
pip install -r requirements.txt
```

If `requirements.txt` doesn't exist, install manually:
```bash
pip install fastapi uvicorn sqlalchemy psycopg2-binary python-dotenv pydantic python-multipart
```

### 3.2 Start Backend Server
```bash
uvicorn main:app --host 0.0.0.0 --port 8004 --reload
```

### 3.3 Verify Backend
Open browser to `http://localhost:8004/docs` to see API documentation.

## Step 4: Flutter App Setup

### 4.1 Install Flutter Dependencies
```bash
cd Skill-Sage
flutter pub get
```

### 4.2 Configure Environment
Create `.env` file in `Skill-Sage/` directory:
```bash
# Skill-Sage/.env
BASE_URL=http://localhost:8004
```

For Android emulator, use your machine's IP:
```bash
BASE_URL=http://********:8004
```

For physical device, use your machine's network IP:
```bash
BASE_URL=http://*************:8004  # Replace with your IP
```

### 4.3 Run Flutter App
```bash
# For Android
flutter run

# For iOS (macOS only)
flutter run -d ios

# For web
flutter run -d chrome
```

## Step 5: Testing the Complete System

### 5.1 Test User Registration and Login

#### Via Flutter App:
1. Open the app
2. Tap "Sign Up" 
3. Fill in details:
   - Name: "Test User"
   - Email: "<EMAIL>"
   - Password: "password123"
4. Complete registration
5. Login with credentials

#### Via API (Alternative):
```bash
# Register user
curl -X POST http://localhost:8004/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>", 
    "password": "password123",
    "role": "JOB_SEEKER"
  }'

# Login
curl -X POST http://localhost:8004/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 5.2 Test Profile Setup

#### Add Skills:
1. Navigate to Profile in Flutter app
2. Add skills like: "JavaScript", "Python", "React", "Node.js"
3. Save profile

#### Upload CV:
1. Go to Profile → Upload Resume
2. Select a PDF file
3. Verify upload success

### 5.3 Test Job Management

#### Create Test Jobs (Employer Account):
```bash
# First create employer account
curl -X POST http://localhost:8004/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Employer User",
    "email": "<EMAIL>",
    "password": "password123", 
    "role": "EMPLOYER"
  }'

# Login as employer and get token
# Then create job
curl -X POST http://localhost:8004/job/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_EMPLOYER_TOKEN" \
  -d '{
    "title": "Frontend Developer",
    "description": "React developer needed",
    "skills": ["JavaScript", "React", "CSS"],
    "location": "New York",
    "salary": 75000,
    "type": "FULL_TIME",
    "company": "Tech Corp",
    "position": "Frontend Developer",
    "expiry": "2024-12-31"
  }'
```

### 5.4 Test Skill Recommendations

#### Via Flutter App:
1. Login as job seeker
2. Navigate to Home screen
3. View "Recommended Skills" section
4. Verify skills appear based on your profile

#### Via API:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/recommend_skills?limit=10
```

### 5.5 Test NEW Job Recommendations

#### Via Flutter App:
1. Ensure you have skills in your profile
2. Navigate to Jobs section
3. Look for "Recommended Jobs" or call the new endpoint

#### Via API:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/recommend_jobs?limit=10
```

Expected response:
```json
{
  "success": true,
  "result": [
    {
      "id": 1,
      "title": "Frontend Developer",
      "company": "Tech Corp",
      "match_score": 85.5,
      "skill_match_count": 3,
      "missing_skills": ["TypeScript"],
      "location": "New York",
      "salary": 75000
    }
  ]
}
```

### 5.6 Test Job Preferences

#### Set Preferences:
```bash
curl -X POST http://localhost:8004/user/job_preferences \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "preferred_locations": ["New York", "San Francisco"],
    "preferred_job_types": ["FULL_TIME"],
    "min_salary": 60000,
    "max_salary": 100000,
    "remote_ok": true
  }'
```

#### Get Preferences:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8004/user/job_preferences
```

## Step 6: Database Verification

### 6.1 Check Tables Created
```sql
-- Connect to database
psql -U skill_sage_user -d skill_sage

-- List all tables
\dt

-- Should see:
-- users, job_seekers, skills, job_seeker_skills
-- jobs, job_applications, bookmarks
-- skill_factors, files, user_resume
-- job_matches, user_job_preferences (NEW)
```

### 6.2 Verify Data
```sql
-- Check users
SELECT id, name, email, role FROM users;

-- Check skills
SELECT COUNT(*) FROM skills;
SELECT COUNT(*) FROM skill_factors;

-- Check job matches (NEW)
SELECT user_id, job_id, match_score FROM job_matches;
```

## Step 7: Troubleshooting

### Common Issues:

#### Backend won't start:
- Check PostgreSQL is running
- Verify database credentials in `.env`
- Check port 8004 is available

#### Flutter app can't connect:
- Verify `BASE_URL` in Flutter `.env`
- Check firewall settings
- For Android emulator, use `********` instead of `localhost`

#### No skill recommendations:
- Ensure skills_recommender database setup completed
- Check `skill_factors` table has data
- Verify user has skills in profile

#### No job recommendations:
- Ensure jobs exist in database
- Verify user has skills in profile
- Check job skills match user skills

### Debug Commands:
```bash
# Check backend logs
uvicorn main:app --host 0.0.0.0 --port 8004 --reload --log-level debug

# Check Flutter logs
flutter logs

# Check database connections
psql -U skill_sage_user -d skill_sage -c "SELECT version();"
```

## Step 8: Production Deployment

### Backend Deployment:
- Use environment variables for production database
- Set up proper CORS origins
- Use production WSGI server like Gunicorn
- Set up SSL/HTTPS

### Flutter Deployment:
- Build release APK: `flutter build apk --release`
- Update BASE_URL to production API
- Configure app signing for Play Store

This completes the setup and testing of the enhanced Skill-Sage system with job recommendations!
