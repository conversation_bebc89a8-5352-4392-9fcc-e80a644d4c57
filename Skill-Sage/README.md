# Skill Sage

A Flutter app for Skill Sage.

## Setup

### Install dependencies

```bash
flutter pub get
```

### Android Wireless Debugging

1. Enable Developer Options and USB debugging on Android device
2. Connect via USB initially: `adb devices`
3. Enable TCP/IP mode: `adb tcpip 5555`
4. Find phone's IP address (Settings → About → Status)
5. Disconnect USB and connect wirelessly: `adb connect <PHONE_IP>:5555`
6. Verify: `flutter devices`
7. Run the app: `flutter run -d <PHONE_IP>:5555`
8. Disconnect: `adb disconnect <PHONE_IP>:5555`

## Development

### List all connected devices

```bash
flutter devices
```

### Run the app on all connected devices

```bash
flutter run
```

### Run the app on a specific device

```bash
flutter run -d <device_id>
```

### Run on wireless Android device

```bash
flutter run -d <PHONE_IP>:5555
```

## Build

### Build an APK (Android)

```bash
flutter build apk
```

### Build an App Bundle (Android)

```bash
flutter build appbundle
```

### Build the app for iOS

```bash
flutter build ios
```
