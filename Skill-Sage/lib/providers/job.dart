part of '_index.dart';

final jobProvider = ChangeNotifierProvider((ref) => JobProvider(ref));

class JobProvider extends ChangeNotifier {
  Ref ref;
  List<dynamic> bookmarks = [];
  List<dynamic> applications = [];
  List<dynamic> recommendedJobs = [];

  Dio get http => ref.read(httpProvider).http;
  JobProvider(this.ref) {
    init();
  }

  void init() {
    http.interceptors.add(InterceptorsWrapper(
      onResponse: (res, handler) async {
        if (res.statusCode == 401) {
          print("Unauthorized");
          // await logout();
        }

        return handler.next(res);
      },
    ));
  }

  Future loadJobs() async {
    final resp = await cather(() => http.get('/job'));
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return resp;
  }

  Future<bool> addBookmark({id}) async {
    final resp = await cather(() => http.post('/job/bookmark/$id'));
    print(resp.success);
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return true;
  }

  Future loadBookmark() async {
    final resp = await cather(() => http.get('/job/bookmarks'));
    if (!resp.success) return throw Exception("failed");
    bookmarks = resp.result;
    notifyListeners();
    return resp;
  }

  Future<bool> removeBookmark({id}) async {
    print(id);
    final resp = await cather(() => http.delete('/job/bookmarks/$id'));
    if (!resp.success) return throw Exception("failed");
    loadBookmark();
    notifyListeners();
    return true;
  }

  Future<bool> addApplication({id}) async {
    final resp = await cather(() => http.post('/job/application/$id'));
    print(resp.success);
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return true;
  }

  Future loadApplications() async {
    final resp = await cather(() => http.get('/job/applications'));
    if (!resp.success) return throw Exception("failed");
    applications = resp.result;
    notifyListeners();
    return resp;
  }

  Future<bool> removeApplication({id}) async {
    print(id);
    final resp = await cather(() => http.delete('/job/application/$id'));
    if (!resp.success) return throw Exception("failed");
    loadApplications();
    return true;
  }

  // Job Recommendations
  Future loadRecommendedJobs({int limit = 20}) async {
    final resp =
        await cather(() => http.get('/user/recommend_jobs?limit=$limit'));
    if (!resp.success) return throw Exception("failed");
    recommendedJobs = resp.result;
    notifyListeners();
    return resp;
  }

  Future loadJobPreferences() async {
    final resp = await cather(() => http.get('/user/job_preferences'));
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return resp;
  }

  Future<bool> updateJobPreferences({
    List<String>? preferredLocations,
    List<String>? preferredJobTypes,
    double? minSalary,
    double? maxSalary,
    bool? remoteOk,
  }) async {
    final data = {
      'preferred_locations': preferredLocations ?? [],
      'preferred_job_types': preferredJobTypes ?? [],
      'min_salary': minSalary,
      'max_salary': maxSalary,
      'remote_ok': remoteOk ?? false,
    };

    final resp =
        await cather(() => http.post('/user/job_preferences', data: data));
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return true;
  }
}
