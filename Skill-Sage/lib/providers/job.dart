part of '_index.dart';

final jobProvider = ChangeNotifierProvider((ref) => JobProvider(ref));

class JobProvider extends ChangeNotifier {
  Ref ref;
  List<dynamic> bookmarks = [];
  List<dynamic> applications = [];
  List<dynamic> recommendedJobs = [];
  List<dynamic> externalJobs = [];
  List<dynamic> allRecommendedJobs = [];

  Dio get http => ref.read(httpProvider).http;
  JobProvider(this.ref) {
    init();
  }

  void init() {
    http.interceptors.add(InterceptorsWrapper(
      onResponse: (res, handler) async {
        if (res.statusCode == 401) {
          print("Unauthorized");
          // await logout();
        }

        return handler.next(res);
      },
    ));
  }

  Future loadJobs() async {
    final resp = await cather(() => http.get('/job'));
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return resp;
  }

  Future<bool> addBookmark({id}) async {
    final resp = await cather(() => http.post('/job/bookmark/$id'));
    print(resp.success);
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return true;
  }

  Future loadBookmark() async {
    final resp = await cather(() => http.get('/job/bookmarks'));
    if (!resp.success) return throw Exception("failed");
    bookmarks = resp.result;
    notifyListeners();
    return resp;
  }

  Future<bool> removeBookmark({id}) async {
    print(id);
    final resp = await cather(() => http.delete('/job/bookmarks/$id'));
    if (!resp.success) return throw Exception("failed");
    loadBookmark();
    notifyListeners();
    return true;
  }

  Future<bool> addApplication({id}) async {
    final resp = await cather(() => http.post('/job/application/$id'));
    print(resp.success);
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return true;
  }

  Future loadApplications() async {
    final resp = await cather(() => http.get('/job/applications'));
    if (!resp.success) return throw Exception("failed");
    applications = resp.result;
    notifyListeners();
    return resp;
  }

  Future<bool> removeApplication({id}) async {
    print(id);
    final resp = await cather(() => http.delete('/job/application/$id'));
    if (!resp.success) return throw Exception("failed");
    loadApplications();
    return true;
  }

  // Job Recommendations
  Future loadRecommendedJobs({int limit = 20}) async {
    final resp =
        await cather(() => http.get('/user/recommend_jobs?limit=$limit'));
    if (!resp.success) return throw Exception("failed");
    recommendedJobs = resp.result;
    notifyListeners();
    return resp;
  }

  Future loadJobPreferences() async {
    final resp = await cather(() => http.get('/user/job_preferences'));
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return resp;
  }

  Future<bool> updateJobPreferences({
    List<String>? preferredLocations,
    List<String>? preferredJobTypes,
    double? minSalary,
    double? maxSalary,
    bool? remoteOk,
  }) async {
    final data = {
      'preferred_locations': preferredLocations ?? [],
      'preferred_job_types': preferredJobTypes ?? [],
      'min_salary': minSalary,
      'max_salary': maxSalary,
      'remote_ok': remoteOk ?? false,
    };

    final resp =
        await cather(() => http.post('/user/job_preferences', data: data));
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return true;
  }

  // External Jobs
  Future loadExternalJobs({int limit = 50, String? source}) async {
    String url = '/user/external_jobs?limit=$limit';
    if (source != null) {
      url += '&source=$source';
    }
    final resp = await cather(() => http.get(url));
    if (!resp.success) return throw Exception("failed");
    externalJobs = resp.result;
    notifyListeners();
    return resp;
  }

  Future loadRecommendedExternalJobs({int limit = 20}) async {
    final resp = await cather(
        () => http.get('/user/recommend_external_jobs?limit=$limit'));
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return resp;
  }

  Future loadAllRecommendedJobs({int limit = 20}) async {
    print('Loading all recommended jobs with limit: $limit');
    final resp =
        await cather(() => http.get('/user/recommend_all_jobs?limit=$limit'));
    print('API response success: ${resp.success}');
    if (resp.success) {
      print('API response result length: ${resp.result?.length ?? 0}');
      if (resp.result != null && resp.result.isNotEmpty) {
        final externalCount =
            resp.result.where((job) => job['is_external'] == true).length;
        final internalCount =
            resp.result.where((job) => job['is_external'] != true).length;
        print('External jobs in response: $externalCount');
        print('Internal jobs in response: $internalCount');
      }
    } else {
      print('API response error: ${resp.error}');
    }
    if (!resp.success) return throw Exception("failed");
    allRecommendedJobs = resp.result;
    notifyListeners();
    return resp;
  }

  Future<bool> scrapeExternalJobs() async {
    final resp = await cather(() => http.post('/user/scrape_jobs'));
    if (!resp.success) return throw Exception("failed");
    notifyListeners();
    return true;
  }
}
