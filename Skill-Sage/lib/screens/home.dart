part of '_index.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int _currentIndex = 0;
  final PageController _pageController = PageController(initialPage: 0);
  String searchQuery = "";
  List<dynamic> allJobs = [];
  List<dynamic> filteredJobs = [];

  @override
  void initState() {
    super.initState();
    _loadAllJobs();
  }

  Future<void> _loadAllJobs() async {
    try {
      final jobProv = ref.read(jobProvider);
      await jobProv.loadAllRecommendedJobs();
      setState(() {
        allJobs = jobProv.allRecommendedJobs;
        filteredJobs = allJobs;
      });
      print('Loaded ${allJobs.length} jobs');
      print(
          'External jobs: ${allJobs.where((job) => job['is_external'] == true).length}');
      print(
          'Internal jobs: ${allJobs.where((job) => job['is_external'] != true).length}');
    } catch (e) {
      print('Error loading jobs: $e');
    }
  }

  void _filterJobs(String query) {
    setState(() {
      searchQuery = query;
      if (query.isEmpty) {
        filteredJobs = allJobs;
      } else {
        filteredJobs = allJobs.where((job) {
          final title = job['title']?.toString().toLowerCase() ?? '';
          final company = job['company']?.toString().toLowerCase() ?? '';
          final location = job['location']?.toString().toLowerCase() ?? '';
          final skills = job['skills']?.join(' ').toLowerCase() ?? '';
          final searchLower = query.toLowerCase();

          return title.contains(searchLower) ||
              company.contains(searchLower) ||
              location.contains(searchLower) ||
              skills.contains(searchLower);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final appTheme = AppTheme.appTheme(context);
    return Scaffold(
      backgroundColor: appTheme.bg1,
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(), // disables scrolling
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: _buildScreens(),
      ),
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: appTheme.primary1,
        currentIndex: _currentIndex,
        selectedItemColor: appTheme.primary2Light,
        type: BottomNavigationBarType.fixed,
        onTap: (index) {
          _pageController.animateToPage(
            index,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
          );
        },
        items: const [
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.home,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'Home page view of recommended skills and jobs',
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.briefcase,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'Internal job postings',
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.bookmark,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'Bookmarked skills',
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.person,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'Users\'s profile view',
          )
        ],
      ),
    );
  }

  List<Widget> _buildScreens() {
    final textTheme = CustomTextTheme.customTextTheme(context).textTheme;
    // final size = MediaQuery.of(context).size;
    // final userProv = ref.read(userProvider);
    return <Widget>[
      SafeArea(
        child: Column(
          children: [
            CustomAppHeader(onSearchChanged: _filterJobs),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Padding(
                  //   padding: const EdgeInsets.only(left: 15.0, top: 15.0),
                  //   child: Text("Popular Stacks", style: textTheme.bodyMedium),
                  // ),
                  // SingleChildScrollView(
                  //   scrollDirection: Axis.horizontal,
                  //   physics: const BouncingScrollPhysics(),
                  //   child: Row(children: [
                  //     SkillCard(
                  //       title: "React",
                  //       subtitle: "JavaScript Library",
                  //       description:
                  //           "The library for web and native user interfaces. Become a React expert. Start today!",
                  //       icon: const Icon(Icons.javascript, size: 20),
                  //       width: size.width * 0.77,
                  //     ),
                  //     SkillCard(
                  //       title: "React",
                  //       subtitle: "JavaScript Library",
                  //       description:
                  //           "The library for web and native user interfaces. Become a React expert. Start today!",
                  //       icon: const Icon(Icons.javascript, size: 20),
                  //       width: size.width * 0.77,
                  //     ),
                  //   ]),
                  // ),
                  // Skills Recommendations Section
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 15.0,
                      top: 15.0,
                      bottom: 10.0,
                    ),
                    child: Text("Skill Recommendations",
                        style: textTheme.bodyMedium),
                  ),
                  SizedBox(
                    height: 120,
                    child: AdvancedFutureBuilder(
                      future: () =>
                          ref.watch(recommenderProvider).loadRecommendations(),
                      builder: (context, snapshot, _) => ListView.builder(
                        scrollDirection: Axis.horizontal,
                        shrinkWrap: true,
                        itemCount: snapshot.result.length,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (_, index) => Container(
                          width: 200,
                          margin: const EdgeInsets.only(left: 15, right: 5),
                          child: SkillCard(
                            title: snapshot.result[index],
                            onPressed: () => Navigator.pushNamed(
                                context, AppRoutes.coursesRoute,
                                arguments: {"skill": snapshot.result[index]}),
                          ),
                        ),
                      ),
                      errorBuilder: (context, error, reload) => Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              error.toString(),
                              style: textTheme.labelSmall,
                            ),
                            TextButton(
                              onPressed: reload,
                              child: const Text("reload"),
                            )
                          ],
                        ),
                      ),
                      emptyBuilder: (context, reload) => Center(
                        child: Text("No skill recommendations",
                            style: textTheme.labelSmall),
                      ),
                    ),
                  ),

                  // Job Recommendations Section
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 15.0,
                      top: 20.0,
                      bottom: 10.0,
                    ),
                    child: Row(
                      children: [
                        Text("Job Recommendations",
                            style: textTheme.bodyMedium),
                        const Spacer(),
                        IconButton(
                          onPressed: () {
                            _showJobFilters();
                          },
                          icon: const Icon(Icons.filter_list),
                          tooltip: 'Filter Jobs',
                        ),
                        TextButton(
                          onPressed: () {
                            _showAllJobs();
                          },
                          child: Text("View All", style: textTheme.labelSmall),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _loadAllJobs,
                      child: filteredJobs.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset("assets/images/not_found.png",
                                      height: 100),
                                  const SizedBox(height: 10),
                                  Text(
                                    searchQuery.isEmpty
                                        ? "No job recommendations available"
                                        : "No jobs found for '$searchQuery'",
                                    style: textTheme.labelSmall,
                                  ),
                                  TextButton(
                                    onPressed: _loadAllJobs,
                                    child: const Text("Reload"),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              physics: const BouncingScrollPhysics(),
                              itemCount: filteredJobs.length,
                              itemBuilder: (context, index) {
                                final job = filteredJobs[index];
                                return _buildJobCard(job, textTheme);
                              },
                            ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
      const JobPostScreen(),
      const BookmarkScreen(),
      const ProfileScreen(),
    ];
  }

  Widget _buildJobCard(Map<String, dynamic> job, TextTheme textTheme) {
    final isExternal = job['is_external'] == true;
    final matchScore = job['match_score']?.toDouble() ?? 0.0;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
      child: InkWell(
        onTap: () {
          if (isExternal && job['apply_url'] != null) {
            _launchUrl(job['apply_url']);
          } else {
            // Show internal job details modal
            _showJobDetails(job);
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          job['title'] ?? 'No Title',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          job['company'] ?? 'Unknown Company',
                          style: textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: isExternal ? Colors.blue : Colors.green,
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              isExternal ? Icons.public : Icons.business,
                              color: Colors.white,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              isExternal ? 'External' : 'Internal',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: matchScore >= 80
                              ? Colors.green
                              : matchScore >= 60
                                  ? Colors.orange
                                  : matchScore > 0
                                      ? Colors.red
                                      : Colors.grey,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          matchScore > 0
                              ? '${matchScore.toInt()}% Match'
                              : isExternal
                                  ? 'New Job'
                                  : '0% Match',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              if (job['location'] != null)
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(job['location'], style: textTheme.bodySmall),
                  ],
                ),
              const SizedBox(height: 8),
              if (job['skills'] != null && job['skills'].isNotEmpty)
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: (job['skills'] as List)
                      .take(3)
                      .map((skill) => Chip(
                            label: Text(
                              skill,
                              style: const TextStyle(fontSize: 12),
                            ),
                            backgroundColor: Colors.blue[50],
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ))
                      .toList(),
                ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (job['salary_min'] != null ||
                      job['salary_max'] != null ||
                      job['salary'] != null)
                    Text(
                      _formatSalary(job),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    )
                  else
                    const SizedBox(),
                  ElevatedButton.icon(
                    onPressed: () {
                      if (isExternal && job['apply_url'] != null) {
                        _launchUrl(job['apply_url']);
                      } else {
                        _showJobDetails(job);
                      }
                    },
                    icon:
                        Icon(isExternal ? Icons.open_in_new : Icons.visibility),
                    label: Text(isExternal ? 'Apply' : 'View'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isExternal ? Colors.blue : Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatSalary(Map<String, dynamic> job) {
    if (job['salary_min'] != null && job['salary_max'] != null) {
      return '\$${(job['salary_min'] as num).toStringAsFixed(0)} - \$${(job['salary_max'] as num).toStringAsFixed(0)}';
    } else if (job['salary_min'] != null) {
      return '\$${(job['salary_min'] as num).toStringAsFixed(0)}+';
    } else if (job['salary'] != null) {
      return '\$${(job['salary'] as num).toStringAsFixed(0)}';
    }
    return 'Salary not specified';
  }

  Future<void> _launchUrl(String url) async {
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewScreen(
            url: url,
            title: 'External Job',
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open $url')),
      );
    }
  }

  void _showJobDetails(Map<String, dynamic> job) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with close button
              Row(
                children: [
                  Expanded(
                    child: Text(
                      job['title'] ?? 'No Title',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Company and location
              Row(
                children: [
                  Icon(Icons.business, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    job['company'] ?? 'Unknown Company',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),

              if (job['location'] != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      job['location'],
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 16),

              // Job details
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Description
                      const Text(
                        'Job Description',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        job['description'] ?? 'No description available',
                        style: const TextStyle(fontSize: 16),
                      ),

                      const SizedBox(height: 20),

                      // Skills required
                      if (job['skills'] != null &&
                          job['skills'].isNotEmpty) ...[
                        Row(
                          children: [
                            const Text(
                              'Required Skills',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            TextButton.icon(
                              onPressed: () {
                                Navigator.pop(context);
                                _showCoursesForSkills(job['skills']);
                              },
                              icon: const Icon(Icons.school, size: 16),
                              label: const Text('View Courses'),
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: (job['skills'] as List)
                              .map((skill) => InkWell(
                                    onTap: () {
                                      Navigator.pop(context);
                                      Navigator.pushNamed(
                                        context,
                                        AppRoutes.coursesRoute,
                                        arguments: {"skill": skill},
                                      );
                                    },
                                    child: Chip(
                                      label: Text(skill),
                                      backgroundColor: Colors.blue[50],
                                    ),
                                  ))
                              .toList(),
                        ),
                        const SizedBox(height: 20),
                      ],

                      // Salary information
                      if (job['salary_min'] != null ||
                          job['salary_max'] != null ||
                          job['salary'] != null) ...[
                        const Text(
                          'Salary',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _formatSalary(job),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green[700],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],

                      // Expiry date
                      if (job['expiry'] != null) ...[
                        const Text(
                          'Application Deadline',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Due ${job['expiry']}',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.red[600],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ],
                  ),
                ),
              ),

              // Apply button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyForInternalJob(job);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Apply for Job',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _applyForInternalJob(Map<String, dynamic> job) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Apply for Job'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You are applying for: ${job['title']}'),
            const SizedBox(height: 16),
            const Text(
                'Your application will be submitted with your current profile and CV.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Here you would submit the application
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Application submitted successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Submit Application'),
          ),
        ],
      ),
    );
  }

  void _showJobFilters() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filter Jobs',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.business),
              title: const Text('Internal Jobs Only'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  filteredJobs = allJobs
                      .where((job) => job['is_external'] != true)
                      .toList();
                });
              },
            ),
            ListTile(
              leading: const Icon(Icons.public),
              title: const Text('External Jobs Only'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  filteredJobs = allJobs
                      .where((job) => job['is_external'] == true)
                      .toList();
                });
              },
            ),
            ListTile(
              leading: const Icon(Icons.star),
              title: const Text('High Match Score (80%+)'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  filteredJobs = allJobs
                      .where((job) => (job['match_score'] ?? 0) >= 80)
                      .toList();
                });
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear),
              title: const Text('Clear Filters'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  filteredJobs = allJobs;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAllJobs() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  const Text(
                    'All Job Recommendations',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                itemCount: filteredJobs.length,
                itemBuilder: (context, index) {
                  final job = filteredJobs[index];
                  return _buildJobCard(job, Theme.of(context).textTheme);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCoursesForSkills(List skills) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  const Text(
                    'Learn Required Skills',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                itemCount: skills.length,
                itemBuilder: (context, index) {
                  final skill = skills[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.blue[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.school,
                          color: Colors.blue,
                        ),
                      ),
                      title: Text(
                        skill,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: const Text('Tap to view available courses'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(
                          context,
                          AppRoutes.coursesRoute,
                          arguments: {"skill": skill},
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
