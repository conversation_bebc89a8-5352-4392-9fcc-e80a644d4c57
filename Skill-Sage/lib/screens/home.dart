part of '_index.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int _currentIndex = 0;
  final PageController _pageController = PageController(initialPage: 0);
  String searchQuery = "";
  List<dynamic> allJobs = [];
  List<dynamic> filteredJobs = [];

  @override
  void initState() {
    super.initState();
    _loadAllJobs();
  }

  Future<void> _loadAllJobs() async {
    try {
      final jobProv = ref.read(jobProvider);
      await jobProv.loadAllRecommendedJobs();
      setState(() {
        allJobs = jobProv.allRecommendedJobs;
        filteredJobs = allJobs;
      });
    } catch (e) {
      print('Error loading jobs: $e');
    }
  }

  void _filterJobs(String query) {
    setState(() {
      searchQuery = query;
      if (query.isEmpty) {
        filteredJobs = allJobs;
      } else {
        filteredJobs = allJobs.where((job) {
          final title = job['title']?.toString().toLowerCase() ?? '';
          final company = job['company']?.toString().toLowerCase() ?? '';
          final location = job['location']?.toString().toLowerCase() ?? '';
          final skills = job['skills']?.join(' ').toLowerCase() ?? '';
          final searchLower = query.toLowerCase();

          return title.contains(searchLower) ||
              company.contains(searchLower) ||
              location.contains(searchLower) ||
              skills.contains(searchLower);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final appTheme = AppTheme.appTheme(context);
    return Scaffold(
      backgroundColor: appTheme.bg1,
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(), // disables scrolling
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: _buildScreens(),
      ),
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: appTheme.primary1,
        currentIndex: _currentIndex,
        selectedItemColor: appTheme.primary2Light,
        type: BottomNavigationBarType.fixed,
        onTap: (index) {
          _pageController.animateToPage(
            index,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
          );
        },
        items: const [
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.home,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'Home page view of recommended skills and jobs',
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.briefcase,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'Internal job postings',
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.globe,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'External job opportunities',
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.bookmark,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'Bookmarked skills',
          ),
          BottomNavigationBarItem(
            icon: Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: Icon(
                CupertinoIcons.person,
                size: 20,
              ),
            ),
            label: '',
            tooltip: 'Users\'s profile view',
          )
        ],
      ),
    );
  }

  List<Widget> _buildScreens() {
    final textTheme = CustomTextTheme.customTextTheme(context).textTheme;
    // final size = MediaQuery.of(context).size;
    // final userProv = ref.read(userProvider);
    return <Widget>[
      SafeArea(
        child: Column(
          children: [
            CustomAppHeader(onSearchChanged: _filterJobs),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Padding(
                  //   padding: const EdgeInsets.only(left: 15.0, top: 15.0),
                  //   child: Text("Popular Stacks", style: textTheme.bodyMedium),
                  // ),
                  // SingleChildScrollView(
                  //   scrollDirection: Axis.horizontal,
                  //   physics: const BouncingScrollPhysics(),
                  //   child: Row(children: [
                  //     SkillCard(
                  //       title: "React",
                  //       subtitle: "JavaScript Library",
                  //       description:
                  //           "The library for web and native user interfaces. Become a React expert. Start today!",
                  //       icon: const Icon(Icons.javascript, size: 20),
                  //       width: size.width * 0.77,
                  //     ),
                  //     SkillCard(
                  //       title: "React",
                  //       subtitle: "JavaScript Library",
                  //       description:
                  //           "The library for web and native user interfaces. Become a React expert. Start today!",
                  //       icon: const Icon(Icons.javascript, size: 20),
                  //       width: size.width * 0.77,
                  //     ),
                  //   ]),
                  // ),
                  // Skills Recommendations Section
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 15.0,
                      top: 15.0,
                      bottom: 10.0,
                    ),
                    child: Text("Skill Recommendations",
                        style: textTheme.bodyMedium),
                  ),
                  SizedBox(
                    height: 120,
                    child: AdvancedFutureBuilder(
                      future: () =>
                          ref.watch(recommenderProvider).loadRecommendations(),
                      builder: (context, snapshot, _) => ListView.builder(
                        scrollDirection: Axis.horizontal,
                        shrinkWrap: true,
                        itemCount: snapshot.result.length,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (_, index) => Container(
                          width: 200,
                          margin: const EdgeInsets.only(left: 15, right: 5),
                          child: SkillCard(
                            title: snapshot.result[index],
                            onPressed: () => Navigator.pushNamed(
                                context, AppRoutes.coursesRoute,
                                arguments: {"skill": snapshot.result[index]}),
                          ),
                        ),
                      ),
                      errorBuilder: (context, error, reload) => Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              error.toString(),
                              style: textTheme.labelSmall,
                            ),
                            TextButton(
                              onPressed: reload,
                              child: const Text("reload"),
                            )
                          ],
                        ),
                      ),
                      emptyBuilder: (context, reload) => Center(
                        child: Text("No skill recommendations",
                            style: textTheme.labelSmall),
                      ),
                    ),
                  ),

                  // Job Recommendations Section
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 15.0,
                      top: 20.0,
                      bottom: 10.0,
                    ),
                    child: Row(
                      children: [
                        Text("Job Recommendations",
                            style: textTheme.bodyMedium),
                        const Spacer(),
                        TextButton(
                          onPressed: () {
                            // Navigate to external jobs screen
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text(
                                      'External Jobs screen coming soon!')),
                            );
                          },
                          child: Text("View All", style: textTheme.labelSmall),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _loadAllJobs,
                      child: filteredJobs.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset("assets/images/not_found.png",
                                      height: 100),
                                  const SizedBox(height: 10),
                                  Text(
                                    searchQuery.isEmpty
                                        ? "No job recommendations available"
                                        : "No jobs found for '$searchQuery'",
                                    style: textTheme.labelSmall,
                                  ),
                                  TextButton(
                                    onPressed: _loadAllJobs,
                                    child: const Text("Reload"),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              physics: const BouncingScrollPhysics(),
                              itemCount: filteredJobs.length,
                              itemBuilder: (context, index) {
                                final job = filteredJobs[index];
                                return _buildJobCard(job, textTheme);
                              },
                            ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
      const JobPostScreen(),
      const ExternalJobsScreen(),
      const BookmarkScreen(),
      const ProfileScreen(),
    ];
  }

  Widget _buildJobCard(Map<String, dynamic> job, TextTheme textTheme) {
    final isExternal = job['is_external'] == true;
    final matchScore = job['match_score']?.toDouble() ?? 0.0;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
      child: InkWell(
        onTap: () {
          if (isExternal && job['apply_url'] != null) {
            _launchUrl(job['apply_url']);
          } else {
            // Show internal job details modal
            _showJobDetails(job);
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          job['title'] ?? 'No Title',
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          job['company'] ?? 'Unknown Company',
                          style: textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isExternal ? Colors.blue : Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          isExternal ? 'External' : 'Internal',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (matchScore > 0) ...[
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: matchScore >= 80
                                ? Colors.green
                                : matchScore >= 60
                                    ? Colors.orange
                                    : Colors.red,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${matchScore.toInt()}% Match',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              if (job['location'] != null)
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(job['location'], style: textTheme.bodySmall),
                  ],
                ),
              const SizedBox(height: 8),
              if (job['skills'] != null && job['skills'].isNotEmpty)
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: (job['skills'] as List)
                      .take(3)
                      .map((skill) => Chip(
                            label: Text(
                              skill,
                              style: const TextStyle(fontSize: 12),
                            ),
                            backgroundColor: Colors.blue[50],
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ))
                      .toList(),
                ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (job['salary_min'] != null ||
                      job['salary_max'] != null ||
                      job['salary'] != null)
                    Text(
                      _formatSalary(job),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    )
                  else
                    const SizedBox(),
                  ElevatedButton.icon(
                    onPressed: () {
                      if (isExternal && job['apply_url'] != null) {
                        _launchUrl(job['apply_url']);
                      } else {
                        _showJobDetails(job);
                      }
                    },
                    icon:
                        Icon(isExternal ? Icons.open_in_new : Icons.visibility),
                    label: Text(isExternal ? 'Apply' : 'View'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isExternal ? Colors.blue : Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatSalary(Map<String, dynamic> job) {
    if (job['salary_min'] != null && job['salary_max'] != null) {
      return '\$${(job['salary_min'] as num).toStringAsFixed(0)} - \$${(job['salary_max'] as num).toStringAsFixed(0)}';
    } else if (job['salary_min'] != null) {
      return '\$${(job['salary_min'] as num).toStringAsFixed(0)}+';
    } else if (job['salary'] != null) {
      return '\$${(job['salary'] as num).toStringAsFixed(0)}';
    }
    return 'Salary not specified';
  }

  Future<void> _launchUrl(String url) async {
    try {
      // For now, we'll use a simple approach
      // In a real app, you'd use url_launcher package
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('External Job'),
          content: Text('This will open: $url'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Here you would actually launch the URL
                // launch(url);
              },
              child: const Text('Open'),
            ),
          ],
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open $url')),
      );
    }
  }

  void _showJobDetails(Map<String, dynamic> job) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                job['title'] ?? 'No Title',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                job['company'] ?? 'Unknown Company',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                job['description'] ?? 'No description available',
                style: const TextStyle(fontSize: 16),
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // Apply for internal job
                  },
                  child: const Text('Apply for Job'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
