import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  Space,
  notification,
  Tag,
  Input,
  Select,
  Card,
  Row,
  Col,
  Statistic,
} from "antd";
import {
  ReloadOutlined,
  ExportOutlined,
  SearchOutlined,
  GlobalOutlined,
} from "@ant-design/icons";
import {
  scrapeExternalJobs,
  getExternalJobs,
  getRecommendedExternalJobs,
  getAllRecommendedJobs,
} from "../../services/job";

const { Option } = Select;

const ExternalJobs = () => {
  const [externalJobs, setExternalJobs] = useState([]);
  const [recommendedJobs, setRecommendedJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [scraping, setScraping] = useState(false);
  const [selectedSource, setSelectedSource] = useState(null);
  const [searchText, setSearchText] = useState("");
  const [activeTab, setActiveTab] = useState("external");

  const [api, contextHolder] = notification.useNotification();

  useEffect(() => {
    loadExternalJobs();
    loadRecommendedJobs();
  }, [selectedSource]);

  const loadExternalJobs = async () => {
    setLoading(true);
    try {
      const response = await getExternalJobs(100, selectedSource);
      if (response.success) {
        setExternalJobs(response.result);
      } else {
        openNotification("error", "Error", "Failed to load external jobs");
      }
    } catch (error) {
      openNotification("error", "Error", "Failed to load external jobs");
    }
    setLoading(false);
  };

  const loadRecommendedJobs = async () => {
    try {
      const response = await getAllRecommendedJobs(50);
      if (response.success) {
        setRecommendedJobs(response.result);
      }
    } catch (error) {
      console.error("Failed to load recommended jobs:", error);
    }
  };

  const handleScrapeJobs = async () => {
    setScraping(true);
    try {
      const response = await scrapeExternalJobs();
      if (response.success) {
        openNotification(
          "success",
          "Jobs Scraped",
          response.result || "Successfully scraped external jobs"
        );
        loadExternalJobs();
      } else {
        openNotification("error", "Error", "Failed to scrape jobs");
      }
    } catch (error) {
      openNotification("error", "Error", "Failed to scrape jobs");
    }
    setScraping(false);
  };

  const openNotification = (type, message, description) => {
    api[type]({
      message,
      description,
    });
  };

  const getSourceColor = (source) => {
    const colors = {
      "StackOverflow": "blue",
      "We Work Remotely": "green",
      "Remote OK": "orange",
      "Greenhouse": "purple",
      "Internal": "gold",
    };
    return colors[source] || "default";
  };

  const filteredJobs = externalJobs.filter((job) =>
    job.title.toLowerCase().includes(searchText.toLowerCase()) ||
    job.company.toLowerCase().includes(searchText.toLowerCase())
  );

  const filteredRecommendedJobs = recommendedJobs.filter((job) =>
    job.title.toLowerCase().includes(searchText.toLowerCase()) ||
    job.company.toLowerCase().includes(searchText.toLowerCase())
  );

  const externalJobColumns = [
    {
      title: "Title",
      dataIndex: "title",
      key: "title",
      width: 200,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: "bold" }}>{text}</div>
          <div style={{ fontSize: "12px", color: "#666" }}>
            {record.company}
          </div>
        </div>
      ),
    },
    {
      title: "Location",
      dataIndex: "location",
      key: "location",
      width: 120,
    },
    {
      title: "Source",
      dataIndex: "source",
      key: "source",
      width: 120,
      render: (source) => (
        <Tag color={getSourceColor(source)}>{source}</Tag>
      ),
    },
    {
      title: "Skills",
      dataIndex: "skills",
      key: "skills",
      width: 200,
      render: (skills) => (
        <div>
          {skills?.slice(0, 3).map((skill, index) => (
            <Tag key={index} size="small">
              {skill}
            </Tag>
          ))}
          {skills?.length > 3 && <Tag size="small">+{skills.length - 3}</Tag>}
        </div>
      ),
    },
    {
      title: "Salary",
      key: "salary",
      width: 120,
      render: (_, record) => {
        if (record.salary_min && record.salary_max) {
          return `$${record.salary_min.toLocaleString()} - $${record.salary_max.toLocaleString()}`;
        } else if (record.salary_min) {
          return `$${record.salary_min.toLocaleString()}+`;
        }
        return "Not specified";
      },
    },
    {
      title: "Posted",
      dataIndex: "posted_date",
      key: "posted_date",
      width: 100,
      render: (date) => {
        if (!date) return "Unknown";
        const postDate = new Date(date);
        const now = new Date();
        const diffTime = Math.abs(now - postDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return `${diffDays}d ago`;
      },
    },
    {
      title: "Action",
      key: "action",
      width: 100,
      render: (_, record) => (
        <Button
          type="primary"
          size="small"
          icon={<ExportOutlined />}
          onClick={() => window.open(record.apply_url, "_blank")}
        >
          Apply
        </Button>
      ),
    },
  ];

  const recommendedJobColumns = [
    ...externalJobColumns.slice(0, -1),
    {
      title: "Match Score",
      dataIndex: "match_score",
      key: "match_score",
      width: 100,
      render: (score) => (
        <Tag color={score >= 80 ? "green" : score >= 60 ? "orange" : "red"}>
          {score}%
        </Tag>
      ),
    },
    {
      title: "Action",
      key: "action",
      width: 100,
      render: (_, record) => (
        <Button
          type="primary"
          size="small"
          icon={<ExportOutlined />}
          onClick={() => {
            if (record.apply_url) {
              window.open(record.apply_url, "_blank");
            } else {
              openNotification("info", "Internal Job", "This is an internal job posting");
            }
          }}
        >
          {record.is_external ? "Apply" : "View"}
        </Button>
      ),
    },
  ];

  const sources = ["StackOverflow", "We Work Remotely", "Remote OK", "Greenhouse"];

  return (
    <div style={{ padding: "20px" }}>
      {contextHolder}
      
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: "20px" }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total External Jobs"
              value={externalJobs.length}
              prefix={<GlobalOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Recommended Jobs"
              value={recommendedJobs.length}
              prefix={<SearchOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Job Sources"
              value={sources.length}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Remote Jobs"
              value={externalJobs.filter(job => job.location.toLowerCase().includes('remote')).length}
            />
          </Card>
        </Col>
      </Row>

      {/* Controls */}
      <div style={{ marginBottom: "20px", display: "flex", gap: "10px", alignItems: "center" }}>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          loading={scraping}
          onClick={handleScrapeJobs}
        >
          Scrape New Jobs
        </Button>
        
        <Select
          placeholder="Filter by source"
          style={{ width: 200 }}
          allowClear
          value={selectedSource}
          onChange={setSelectedSource}
        >
          {sources.map(source => (
            <Option key={source} value={source}>{source}</Option>
          ))}
        </Select>

        <Input.Search
          placeholder="Search jobs..."
          style={{ width: 300 }}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
        />

        <div style={{ marginLeft: "auto" }}>
          <Button.Group>
            <Button
              type={activeTab === "external" ? "primary" : "default"}
              onClick={() => setActiveTab("external")}
            >
              All External Jobs ({filteredJobs.length})
            </Button>
            <Button
              type={activeTab === "recommended" ? "primary" : "default"}
              onClick={() => setActiveTab("recommended")}
            >
              Recommended Jobs ({filteredRecommendedJobs.length})
            </Button>
          </Button.Group>
        </div>
      </div>

      {/* Jobs Table */}
      <Table
        columns={activeTab === "external" ? externalJobColumns : recommendedJobColumns}
        dataSource={activeTab === "external" ? filteredJobs : filteredRecommendedJobs}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} jobs`,
        }}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default ExternalJobs;
