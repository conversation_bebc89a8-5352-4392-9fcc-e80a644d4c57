import requests
import json
import isodate


def get_videos_for_skill(api_key, skill, level="beginner", max_videos=10):
    """
    Get real YouTube videos for a specific skill

    Args:
        api_key: Your YouTube Data API key
        skill: The skill you want to learn (e.g., "python", "javascript", "react")
        level: "beginner", "intermediate", or "advanced"
        max_videos: Number of videos to return (max 50)

    Returns:
        Dictionary with video information in JSON format
    """

    # Construct search query based on skill and level
    if level == "beginner":
        query = f"{skill} tutorial for beginners learn {skill}"
    elif level == "intermediate":
        query = f"{skill} intermediate tutorial advanced {skill}"
    else:  # advanced
        query = f"{skill} advanced tutorial expert {skill} masterclass"

    # Step 1: Search for videos
    search_url = "https://www.googleapis.com/youtube/v3/search"
    search_params = {
        'part': 'snippet',
        'q': query,
        'type': 'video',
        'maxResults': min(max_videos, 50),  # YouTube API limit
        'order': 'relevance',
        'videoDuration': 'medium',  # 4-20 minutes
        'videoDefinition': 'high',
        'relevanceLanguage': 'en',
        'key': api_key
    }

    try:
        # Make search request
        search_response = requests.get(search_url, params=search_params)
        search_response.raise_for_status()
        search_data = search_response.json()

        if 'items' not in search_data or not search_data['items']:
            return {
                "error": "No videos found",
                "skill": skill,
                "level": level,
                "videos": []
            }

        # Extract video IDs
        video_ids = [item['id']['videoId'] for item in search_data['items']]

        # Step 2: Get detailed video information
        details_url = "https://www.googleapis.com/youtube/v3/videos"
        details_params = {
            'part': 'contentDetails,snippet,statistics',
            'id': ','.join(video_ids),
            'key': api_key
        }

        details_response = requests.get(details_url, params=details_params)
        details_response.raise_for_status()
        details_data = details_response.json()

        # Step 3: Format the results
        videos = []
        for item in details_data.get('items', []):
            # Convert duration from ISO 8601 to readable format
            duration_iso = item['contentDetails']['duration']
            duration_seconds = int(isodate.parse_duration(
                duration_iso).total_seconds())

            # Format duration as MM:SS or HH:MM:SS
            if duration_seconds >= 3600:
                hours = duration_seconds // 3600
                minutes = (duration_seconds % 3600) // 60
                seconds = duration_seconds % 60
                duration_formatted = f"{hours}:{minutes:02d}:{seconds:02d}"
            else:
                minutes = duration_seconds // 60
                seconds = duration_seconds % 60
                duration_formatted = f"{minutes}:{seconds:02d}"

            # Clean description
            description = item['snippet']['description']
            if len(description) > 200:
                description = description[:200] + "..."

            video_info = {
                "name": item['snippet']['title'],
                "description": description,
                "thumbnail": item['snippet']['thumbnails']['high']['url'],
                "url": f"https://www.youtube.com/watch?v={item['id']}",
                "duration": duration_formatted,
                "view_count": int(item['statistics'].get('viewCount', 0)),
                "like_count": int(item['statistics'].get('likeCount', 0)),
                "published_date": item['snippet']['publishedAt'][:10],
                "channel_name": item['snippet']['channelTitle']
            }
            videos.append(video_info)

        # Sort by view count (most popular first)
        videos.sort(key=lambda x: x['view_count'], reverse=True)

        return {
            "skill": skill,
            "level": level,
            "total_results": len(videos),
            "videos": videos
        }

    except requests.exceptions.RequestException as e:
        return {
            "error": f"API request failed: {str(e)}",
            "skill": skill,
            "level": level,
            "videos": []
        }
    except Exception as e:
        return {
            "error": f"Unexpected error: {str(e)}",
            "skill": skill,
            "level": level,
            "videos": []
        }


# Example usage
if __name__ == "__main__":
    # Replace with your actual YouTube Data API key
    API_KEY = "AIzaSyA5NCk9OV_RaHkx73p9BNSL1BjgrfOyFBM"

    # Example 1: Python for beginners
    print("Searching for Python beginner videos...")
    result = get_videos_for_skill(API_KEY, "python", "beginner", 5)

    # Print results as formatted JSON
    print(json.dumps(result, indent=2))

    # Example 2: JavaScript intermediate
    print("\nSearching for JavaScript intermediate videos...")
    js_result = get_videos_for_skill(API_KEY, "javascript", "intermediate", 3)
    print(json.dumps(js_result, indent=2))

    # Save to file
    with open('skill_videos.json', 'w', encoding='utf-8') as f:
        json.dump({
            "python_beginner": result,
            "javascript_intermediate": js_result
        }, f, indent=2, ensure_ascii=False)

    print("\nResults saved to 'skill_videos.json'")

# Quick function for different skills


def search_popular_skills(api_key):
    """Search for videos in popular programming skills"""

    skills = [
        ("python", "beginner"),
        ("javascript", "beginner"),
        ("react", "intermediate"),
        ("data science", "beginner"),
        ("machine learning", "beginner")
    ]

    all_results = {}

    for skill, level in skills:
        print(f"Searching for {skill} ({level}) videos...")
        result = get_videos_for_skill(api_key, skill, level, 3)
        all_results[f"{skill}_{level}"] = result

    return all_results
