{"python_beginner": {"skill": "python", "level": "beginner", "total_results": 5, "videos": [{"name": "Python Tutorial for Beginners - Learn Python in 5 Hours [FULL COURSE]", "description": "►  Grab your free IT Fundamentals Roadmap: https://bit.ly/3GV5Noy\n\n💚 Hands-On course to learn the complete SDLC - from code to deployment: https://bit.ly/43gOF3f\n\nPython Tutorial for Beginners | Full ...", "thumbnail": "https://i.ytimg.com/vi/t8pPdKYpowI/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=t8pPdKYpowI", "duration": "5:31:30", "view_count": 6458897, "like_count": 165358, "published_date": "2021-03-05", "channel_name": "TechWorld with <PERSON>"}, {"name": "you need to learn Python RIGHT NOW!! // EP 1", "description": "What I use to learn (the BEST IT training): https://ntck.co/itprotv (30% off FOREVER) *affiliate link\n\n\n🔎🔎FREE Python Lab: https://ntck.co/python\nSupport the course: https://ntck.co/pythonrightnow\n\n\n🔥...", "thumbnail": "https://i.ytimg.com/vi/mRMmlo_Uqcs/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=mRMmlo_Uqcs", "duration": "17:42", "view_count": 2716469, "like_count": 109981, "published_date": "2021-08-14", "channel_name": "NetworkChuck"}, {"name": "Learn Python in Less than 10 Minutes for Beginners (Fast & Easy)", "description": "In this crash course I'll be teaching you the basics of Python in less than 10 minutes. Python is super easy to learn compared to the other languages!\n\nDownload Python: https://www.python.org/\nDownloa...", "thumbnail": "https://i.ytimg.com/vi/fWjsdhR3z3c/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=fWjsdhR3z3c", "duration": "10:30", "view_count": 911442, "like_count": 21256, "published_date": "2021-05-26", "channel_name": "Indently"}, {"name": "Do THIS instead of watching endless tutorials - how I’d learn Python FAST…", "description": "🎓 These are two of the best beginner-friendly Python resources I recommend:\n\n🔹 Python Programming Fundamentals (Datacamp) (https://datacamp.pxf.io/QjG9BM)\n🔹 Associate Python Developer Certificate (Dat...", "thumbnail": "https://i.ytimg.com/vi/mB0EBW-vDSQ/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=mB0EBW-vDSQ", "duration": "10:34", "view_count": 243075, "like_count": 11326, "published_date": "2025-05-16", "channel_name": "Tech With <PERSON>"}, {"name": "Python Tutorial for Beginners - Learn Python in 4 Hours [FULL COURSE]", "description": "In this video I explain how to learn to code in Python in quick and easy steps Follow these steps to learn to code in any language as fast as possible. I describe how to think like a programmer, and h...", "thumbnail": "https://i.ytimg.com/vi/MRuoJrmqHFk/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=MRuoJrmqHFk", "duration": "59:31", "view_count": 421, "like_count": 17, "published_date": "2022-02-15", "channel_name": "<PERSON><PERSON>"}]}, "javascript_intermediate": {"skill": "javascript", "level": "intermediate", "total_results": 3, "videos": [{"name": "100+ JavaScript Concepts you Need to Know", "description": "The ultimate 10 minute JavaScript course that quickly breaks down over 100 key concepts every web developer should know. Learn the basics of JS and start building apps on the web, mobile, desktop, and...", "thumbnail": "https://i.ytimg.com/vi/lkIFF4maKMU/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=lkIFF4maKMU", "duration": "12:24", "view_count": 2655476, "like_count": 98745, "published_date": "2022-11-22", "channel_name": "Fireship"}, {"name": "Closures Explained in 100 Seconds // Tricky JavaScript Interview Prep", "description": "What is a JavaScript Closure? Learn the how closures allow functions to \"remember\" outside their local scope https://fireship.io/tags/javascript/\n\n#js #programming #100SecondsOfCode\n\n🔗 Resources\n\nMDN ...", "thumbnail": "https://i.ytimg.com/vi/vKJpN5FAeF4/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=vKJpN5FAeF4", "duration": "4:57", "view_count": 391676, "like_count": 22306, "published_date": "2021-06-17", "channel_name": "Fireship"}, {"name": "How to Learn JavaScript FAST in 2025", "description": "If you want to get an easy introduction to JavaScript to help you take your code to the next level, check out this free resource from Hubspot! https://clickhubspot.com/ibs4\n\n-----------\n\n🚀 Learn JavaS...", "thumbnail": "https://i.ytimg.com/vi/xB3ZmUH6GqU/hqdefault.jpg", "url": "https://www.youtube.com/watch?v=xB3ZmUH6GqU", "duration": "12:32", "view_count": 117007, "like_count": 6074, "published_date": "2025-04-01", "channel_name": "<PERSON>"}]}}