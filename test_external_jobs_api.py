#!/usr/bin/env python3
"""
Test script to verify external jobs API is working correctly
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8004"
# You'll need to replace this with a valid token
TOKEN = "your_token_here"

def test_external_jobs():
    """Test the external jobs endpoint"""
    print("Testing external jobs endpoint...")
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test external jobs endpoint
        response = requests.get(f"{BASE_URL}/user/external_jobs?limit=10", headers=headers)
        print(f"External jobs status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                jobs = data.get("result", [])
                print(f"Found {len(jobs)} external jobs")
                for job in jobs[:3]:  # Show first 3
                    print(f"  - {job['title']} at {job['company']} (Enabled: {job.get('is_enabled', 'N/A')})")
            else:
                print(f"API returned error: {data.get('error', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Error testing external jobs: {e}")

def test_recommend_all_jobs():
    """Test the combined recommendations endpoint"""
    print("\nTesting combined recommendations endpoint...")
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test combined recommendations endpoint
        response = requests.get(f"{BASE_URL}/user/recommend_all_jobs?limit=20", headers=headers)
        print(f"Combined recommendations status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                jobs = data.get("result", [])
                external_jobs = [job for job in jobs if job.get('is_external')]
                internal_jobs = [job for job in jobs if not job.get('is_external')]
                
                print(f"Found {len(jobs)} total jobs")
                print(f"  - External: {len(external_jobs)}")
                print(f"  - Internal: {len(internal_jobs)}")
                
                print("\nSample external jobs:")
                for job in external_jobs[:3]:
                    print(f"  - {job['title']} at {job['company']} (Match: {job.get('match_score', 0)}%)")
                    
                print("\nSample internal jobs:")
                for job in internal_jobs[:3]:
                    print(f"  - {job['title']} at {job['company']} (Match: {job.get('match_score', 0)}%)")
                    
            else:
                print(f"API returned error: {data.get('error', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Error testing combined recommendations: {e}")

def test_scrape_jobs():
    """Test the job scraping endpoint"""
    print("\nTesting job scraping endpoint...")
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test scraping endpoint
        response = requests.post(f"{BASE_URL}/user/scrape_jobs", headers=headers)
        print(f"Scraping status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"Scraping result: {data.get('result', 'No result message')}")
            else:
                print(f"API returned error: {data.get('error', 'Unknown error')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Error testing job scraping: {e}")

if __name__ == "__main__":
    print("External Jobs API Test")
    print("=" * 50)
    
    if TOKEN == "your_token_here":
        print("⚠️  Please update the TOKEN variable with a valid authentication token")
        print("You can get this from the browser's developer tools when logged into the app")
        print()
    
    test_external_jobs()
    test_recommend_all_jobs()
    # test_scrape_jobs()  # Uncomment to test scraping
    
    print("\n" + "=" * 50)
    print("Test completed!")
